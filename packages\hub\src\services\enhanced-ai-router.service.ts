import axios from 'axios';
import { logger } from '../utils/logger';
import { usageTrackingService } from './usage-tracking.service';
import { modelSelectorService } from './model-selector.service';
import { promptManager } from './prompt-manager.service';
import { ParsedTrip } from '@travelviz/shared';

/**
 * Enhanced AI Router Service
 * Orchestrates model selection, usage tracking, and request execution
 * Requirements: 2.1, 2.2, 5.1, 5.2, 5.3 from requirements.md
 */

export interface AIResponse {
  content: string;
  modelUsed: string;
  inputTokens: number;
  outputTokens: number;
  cost: number;
  duration: number;
}

export interface UsageStatistics {
  totalRequests: number;
  totalCost: number;
  modelBreakdown: Record<string, {
    requests: number;
    cost: number;
    successRate: number;
  }>;
}

export class EnhancedAIRouterService {
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY_BASE = 1000; // 1 second base delay
  private readonly REQUEST_TIMEOUT = 60000; // 60 seconds

  /**
   * Main parsing method with intelligent routing - Requirements 2.1, 2.2
   */
  async parseContent(content: string, source: string, userId: string): Promise<ParsedTrip> {
    const startTime = Date.now();
    let selectedModel: string | null = null;
    let fallbackAttempts = 0;

    try {
      // Step 1: Intelligent model selection
      const modelSelection = await modelSelectorService.selectModel(content);
      selectedModel = modelSelection.modelId;
      
      logger.info('Model selected for parsing', {
        modelId: selectedModel,
        provider: modelSelection.provider,
        reason: modelSelection.reason,
        estimatedCost: modelSelection.estimatedCost,
        userId
      });

      // Step 2: Execute with selected model
      let aiResponse: AIResponse;
      try {
        aiResponse = await this.executeWithModel(
          selectedModel,
          promptManager.getSystemPrompt(selectedModel),
          content
        );
      } catch (error) {
        // Step 3: Handle failures with fallback chain - Requirements 5.1, 5.2
        logger.warn('Primary model failed, trying fallbacks', { 
          primaryModel: selectedModel, 
          error: error instanceof Error ? error.message : String(error)
        });
        
        aiResponse = await this.executeWithFallbacks(
          modelSelection.fallbackChain,
          content,
          error as Error
        );
        fallbackAttempts = 1; // At least one fallback was used
      }

      // Step 4: Track usage
      await usageTrackingService.trackRequest(
        aiResponse.modelUsed,
        aiResponse.inputTokens,
        aiResponse.outputTokens
      );

      // Step 5: Parse and validate response
      const parsedTrip = this.parseAIResponse(aiResponse.content, source);
      
      // Step 6: Add usage metadata to the parsed trip for tracking
      // Per design.md: Include model usage information in metadata
      if (parsedTrip.metadata) {
        parsedTrip.metadata = {
          ...parsedTrip.metadata,
          modelUsed: aiResponse.modelUsed,
          inputTokens: aiResponse.inputTokens,
          outputTokens: aiResponse.outputTokens,
          cost: aiResponse.cost,
          fallbackAttempts
        };
      }
      
      // Step 7: Log successful parsing
      const duration = Date.now() - startTime;
      logger.info('Content parsed successfully', {
        modelUsed: aiResponse.modelUsed,
        duration,
        inputTokens: aiResponse.inputTokens,
        outputTokens: aiResponse.outputTokens,
        cost: aiResponse.cost,
        fallbackAttempts,
        userId
      });

      return parsedTrip;

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Content parsing failed completely', {
        selectedModel,
        duration,
        fallbackAttempts,
        error: error instanceof Error ? error.message : String(error),
        userId
      });
      
      throw new Error(`AI parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute request with specific model
   */
  async executeWithModel(modelId: string, prompt: string, content: string): Promise<AIResponse> {
    const startTime = Date.now();
    
    try {
      // Get model-specific configuration
      const systemPrompt = promptManager.getSystemPrompt(modelId);
      const formatInstructions = promptManager.getFormatInstructions(modelId);
      
      // Estimate tokens for tracking
      const tokenEstimate = modelSelectorService.estimateTokens(content);
      
      // Build full prompt
      const fullPrompt = `${systemPrompt}\n\n${formatInstructions}\n\nContent to parse:\n${content}`;
      
      let response: any;
      
      // Handle different providers
      if (modelId.startsWith('google/')) {
        response = await this.callGoogleAPI(modelId, systemPrompt, content);
      } else if (modelId.startsWith('moonshotai/')) {
        response = await this.callOpenRouterAPI(modelId, systemPrompt, content);
      } else {
        response = await this.callOpenRouterAPI(modelId, systemPrompt, content);
      }
      
      const duration = Date.now() - startTime;
      
      return {
        content: response.content,
        modelUsed: modelId,
        inputTokens: tokenEstimate.inputTokens,
        outputTokens: tokenEstimate.outputTokens,
        cost: 0, // Will be calculated by usage tracking
        duration
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Model execution failed', {
        modelId,
        duration,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Handle rate limits and fallbacks - Requirements 5.1, 5.2, 5.3
   */
  async handleRateLimit(modelId: string, error: Error): Promise<string> {
    logger.warn('Rate limit detected, selecting fallback', { modelId, error: error.message });
    
    // Get fallback chain for the failed model
    const tokenEstimate = modelSelectorService.estimateTokens(''); // Empty content for fallback selection
    const fallbackChain = modelSelectorService.getFallbackChain(modelId, tokenEstimate.complexity);
    
    // Find first available fallback
    for (const fallbackModel of fallbackChain) {
      const isAvailable = await usageTrackingService.isModelAvailable(fallbackModel);
      if (isAvailable) {
        logger.info('Selected fallback model', { 
          originalModel: modelId, 
          fallbackModel,
          reason: 'Rate limit on primary model'
        });
        return fallbackModel;
      }
    }
    
    throw new Error('All models are rate limited or unavailable');
  }

  /**
   * Get usage statistics
   */
  async getUsageStats(): Promise<UsageStatistics> {
    try {
      const allUsage = await usageTrackingService.getAllUsage();
      
      let totalRequests = 0;
      let totalCost = 0;
      const modelBreakdown: Record<string, { requests: number; cost: number; successRate: number }> = {};
      
      for (const [modelId, usage] of Object.entries(allUsage)) {
        totalRequests += usage.requestCount;
        totalCost += usage.inputTokens * 0.001; // Rough cost calculation
        
        modelBreakdown[modelId] = {
          requests: usage.requestCount,
          cost: usage.inputTokens * 0.001,
          successRate: 0.95 // Placeholder - would need actual tracking
        };
      }
      
      return {
        totalRequests,
        totalCost,
        modelBreakdown
      };
    } catch (error) {
      logger.error('Failed to get usage statistics', { error });
      return {
        totalRequests: 0,
        totalCost: 0,
        modelBreakdown: {}
      };
    }
  }

  /**
   * Execute with fallback chain
   */
  private async executeWithFallbacks(
    fallbackChain: string[],
    content: string,
    originalError: Error
  ): Promise<AIResponse> {
    let lastError = originalError;
    
    for (const fallbackModel of fallbackChain) {
      try {
        logger.info('Trying fallback model', { fallbackModel });
        
        const isAvailable = await usageTrackingService.isModelAvailable(fallbackModel);
        if (!isAvailable) {
          logger.warn('Fallback model not available', { fallbackModel });
          continue;
        }
        
        return await this.executeWithModel(
          fallbackModel,
          promptManager.getSystemPrompt(fallbackModel),
          content
        );
      } catch (error) {
        lastError = error as Error;
        logger.warn('Fallback model failed', { 
          fallbackModel, 
          error: error instanceof Error ? error.message : String(error)
        });
        
        // Implement exponential backoff with jitter - Requirements 5.3
        const delay = this.RETRY_DELAY_BASE * Math.pow(2, fallbackChain.indexOf(fallbackModel));
        const jitter = Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, delay + jitter));
      }
    }
    
    throw new Error(`All fallback models failed. Last error: ${lastError.message}`);
  }

  /**
   * Call Google Gemini API
   */
  private async callGoogleAPI(modelId: string, systemPrompt: string, content: string): Promise<{ content: string }> {
    const apiKey = process.env.GOOGLE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Google Gemini API key not configured');
    }

    // Map model ID to Google API model name
    const modelMap: Record<string, string> = {
      'google/gemini-2.5-pro': 'gemini-2.5-pro',
      'google/gemini-2.5-flash': 'gemini-2.5-flash',
      'google/gemini-2.0-flash': 'gemini-2.0-flash-exp'
    };

    const googleModel = modelMap[modelId] || 'gemini-2.0-flash-exp';
    
    logger.info('Making Google Gemini API request', {
      modelId,
      googleModel,
      contentLength: content.length,
      systemPromptLength: systemPrompt.length,
      timeout: this.REQUEST_TIMEOUT
    });

    try {
      const requestPayload = {
        contents: [{
          parts: [{
            text: `${systemPrompt}\n\nContent to parse:\n${content}`
          }]
        }],
        generationConfig: {
          temperature: 0.2,
          maxOutputTokens: 8192,
          responseMimeType: 'application/json'
        }
      };

      logger.debug('Google Gemini request payload', {
        googleModel,
        temperature: requestPayload.generationConfig.temperature,
        maxOutputTokens: requestPayload.generationConfig.maxOutputTokens,
        responseMimeType: requestPayload.generationConfig.responseMimeType
      });

      const response = await axios.post(
        `https://generativelanguage.googleapis.com/v1beta/models/${googleModel}:generateContent?key=${apiKey}`,
        requestPayload,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: this.REQUEST_TIMEOUT
        }
      );

      logger.info('Google Gemini API response received', {
        status: response.status,
        statusText: response.statusText,
        hasData: !!response.data,
        hasCandidates: !!response.data?.candidates,
        candidatesLength: response.data?.candidates?.length || 0
      });

      if (response.data?.error) {
        logger.error('Google Gemini API returned error', {
          error: response.data.error,
          modelId,
          googleModel
        });
        throw new Error(`Google Gemini API error: ${response.data.error.message || 'Unknown error'}`);
      }

      const aiResponse = response.data.candidates?.[0]?.content?.parts?.[0]?.text;
      if (!aiResponse) {
        logger.error('No response content from Google Gemini API', {
          responseData: response.data,
          modelId,
          googleModel
        });
        throw new Error('No response from Google Gemini API');
      }

      logger.info('Google Gemini API call successful', {
        modelId,
        googleModel,
        responseLength: aiResponse.length
      });

      return { content: aiResponse };

    } catch (error) {
      if (axios.isAxiosError(error)) {
        logger.error('Google Gemini API request failed', {
          modelId,
          googleModel,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
          code: error.code
        });

        if (error.code === 'ECONNABORTED') {
          throw new Error(`Google Gemini API timeout after ${this.REQUEST_TIMEOUT}ms`);
        }

        if (error.response?.status === 429) {
          throw new Error('Google Gemini API rate limit exceeded');
        }

        if (error.response?.status === 401 || error.response?.status === 403) {
          throw new Error('Google Gemini API authentication failed - check API key');
        }

        throw new Error(`Google Gemini API error: ${error.response?.data?.error?.message || error.message}`);
      }

      logger.error('Unexpected error in Google Gemini API call', {
        modelId,
        googleModel,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Call OpenRouter API
   */
  private async callOpenRouterAPI(modelId: string, systemPrompt: string, content: string): Promise<{ content: string }> {
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    logger.info('Making OpenRouter API request', {
      modelId,
      contentLength: content.length,
      systemPromptLength: systemPrompt.length,
      timeout: this.REQUEST_TIMEOUT
    });

    try {
      const requestPayload = {
        model: modelId,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: content
          }
        ],
        temperature: 0.3,
        max_tokens: 4000,
        response_format: { type: 'json_object' }
      };

      logger.debug('OpenRouter request payload', {
        model: requestPayload.model,
        messageCount: requestPayload.messages.length,
        temperature: requestPayload.temperature,
        maxTokens: requestPayload.max_tokens
      });

      const response = await axios.post(
        'https://openrouter.ai/api/v1/chat/completions',
        requestPayload,
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://travelviz.app',
            'X-Title': 'TravelViz'
          },
          timeout: this.REQUEST_TIMEOUT
        }
      );

      logger.info('OpenRouter API response received', {
        status: response.status,
        statusText: response.statusText,
        hasData: !!response.data,
        hasChoices: !!response.data?.choices,
        choicesLength: response.data?.choices?.length || 0
      });

      if (response.data?.error) {
        logger.error('OpenRouter API returned error', {
          error: response.data.error,
          modelId
        });
        throw new Error(`OpenRouter API error: ${response.data.error.message || 'Unknown error'}`);
      }

      const aiResponse = response.data.choices?.[0]?.message?.content;
      if (!aiResponse) {
        logger.error('No response content from OpenRouter API', {
          responseData: response.data,
          modelId
        });
        throw new Error('No response from OpenRouter API');
      }

      logger.info('OpenRouter API call successful', {
        modelId,
        responseLength: aiResponse.length
      });

      return { content: aiResponse };

    } catch (error) {
      if (axios.isAxiosError(error)) {
        logger.error('OpenRouter API request failed', {
          modelId,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
          code: error.code
        });

        if (error.code === 'ECONNABORTED') {
          throw new Error(`OpenRouter API timeout after ${this.REQUEST_TIMEOUT}ms`);
        }

        if (error.response?.status === 429) {
          throw new Error('OpenRouter API rate limit exceeded');
        }

        if (error.response?.status === 401) {
          throw new Error('OpenRouter API authentication failed - check API key');
        }

        throw new Error(`OpenRouter API error: ${error.response?.data?.error?.message || error.message}`);
      }

      logger.error('Unexpected error in OpenRouter API call', {
        modelId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Parse AI response into ParsedTrip format
   */
  private parseAIResponse(content: string, source: string): ParsedTrip {
    try {
      const parsed = JSON.parse(content);
      
      // Check for error response
      if (parsed.error) {
        throw new Error(parsed.message || 'AI parsing returned error');
      }
      
      // Validate required fields
      if (!parsed.title || !parsed.activities) {
        throw new Error('Invalid AI response - missing required fields');
      }
      
      // Ensure metadata exists
      if (!parsed.metadata) {
        parsed.metadata = {
          source: source as any,
          confidence: 0.8,
          warnings: [],
          parseDate: new Date().toISOString(),
          version: '1.0'
        };
      }
      
      return parsed as ParsedTrip;
    } catch (error) {
      logger.error('Failed to parse AI response', { 
        error: error instanceof Error ? error.message : String(error),
        contentPreview: content.substring(0, 200)
      });
      throw new Error('Invalid JSON response from AI model');
    }
  }
}

// Export singleton instance
export const enhancedAIRouterService = new EnhancedAIRouterService();