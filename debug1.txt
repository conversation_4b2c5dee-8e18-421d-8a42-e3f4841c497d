PS C:\Users\<USER>\Travelviz\Travelviz> pnpm dev

> travelviz@1.0.0 dev C:\Users\<USER>\Travelviz\Travelviz
> node scripts/dev-services-enhanced.js

Starting TravelViz development servers...
[hub] Starting API server... 
[web] Starting Next.js app...

⠴ Waiting for services to start...[hub] > @travelviz/hub@1.0.0 dev C:\Users\<USER>\Travelviz\Travelviz\packages\hub
[hub] 🔄 Compiling TypeScript...
[hub] > npx tsx watch src/index.ts
[web] > @travelviz/web@1.0.0 dev C:\Users\<USER>\Travelviz\Travelviz\packages\web
[web] > next dev
⠦ Waiting for services to start...[web]    ▲ Next.js 15.3.5
[web]    - Local:        http://localhost:3000
[web]    - Network:      http://*************:3000
[web]    - Environments: .env.local
[web] 🔄 Initializing Next.js...
⠦ Waiting for services to start...[hub] 2025-07-17T19:54:39.734Z [WARN] Travelpayouts API key not configured 
⠧ Waiting for services to start...[web]  ✓ Ready in 2.6s
[web]  ✓ Compiled /middleware in 194ms
⠇ Waiting for services to start...[hub] 2025-07-17T19:54:40.037Z [WARN] Mapbox access token not found - geocoding will be disabled 
⠏ Waiting for services to start...[web]  ✓ Compiled (108 modules)
⠋ Waiting for services to start...[hub] 2025-07-17T19:54:40.150Z [INFO] Usage tracking service initialized with reset notification listener 
[hub] 2025-07-17T19:54:40.153Z [INFO] Model-specific prompts initialized {
[hub]   "promptCount": 5,
[hub]   "models": [
[hub]     "moonshotai/kimi-k2:free",
[hub]     "google/gemini-2.5-pro",
[hub]     "google/gemini-2.5-flash",
[hub]     "google/gemini-2.0-flash",
[hub]     "openai/gpt-4.1-nano"
[hub]   ]
[hub] }
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
⠸ Waiting for services to start...[hub] 2025-07-17T19:54:40.519Z [INFO] Loading environment variables... {
[hub]   "cwd": "C:\\Users\\<USER>\\Travelviz\\Travelviz\\packages\\hub",
[hub]   "__dirname": "C:\\Users\\<USER>\\Travelviz\\Travelviz\\packages\\hub\\src\\utils",
[hub]   "rootDir": "C:\\Users\\<USER>\\Travelviz\\Travelviz\\packages\\hub",
[hub]   "nodeEnv": "development"
[hub] }
[hub] 2025-07-17T19:54:40.519Z [INFO] Checking for environment file: C:\Users\<USER>\Travelviz\Travelviz\packages\hub\.env.local
[hub] 2025-07-17T19:54:40.520Z [INFO] Found environment file: C:\Users\<USER>\Travelviz\Travelviz\packages\hub\.env.local
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment loaded from: C:\Users\<USER>\Travelviz\Travelviz\packages\hub\.env.local
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment status: {
[hub]   "NODE_ENV": "development",
[hub]   "OPENROUTER_API_KEY": true,
[hub]   "GOOGLE_GEMINI_API_KEY": true,
[hub]   "SUPABASE_URL": true,
[hub]   "UPSTASH_REDIS_URL": true
[hub] }
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment variable JWT_SECRET is set (value hidden)
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment variable SUPABASE_URL is set
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment variable SUPABASE_SERVICE_ROLE_KEY is set (value hidden)
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment variable SUPABASE_JWT_SECRET is set (value hidden)
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment variable OPENROUTER_API_KEY is set (value hidden)
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment variable MAPBOX_ACCESS_TOKEN is set (value hidden)
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment variable GOOGLE_PLACES_API_KEY is set (value hidden)
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment variable PORT is set
[hub] 2025-07-17T19:54:40.521Z [INFO] Environment variable NODE_ENV is set
[hub] 2025-07-17T19:54:40.521Z [INFO] JWT_SECRET validation passed
[hub] 2025-07-17T19:54:40.522Z [INFO] MAPBOX_ACCESS_TOKEN validation passed
[hub] 2025-07-17T19:54:40.522Z [INFO] GOOGLE_PLACES_API_KEY validation passed
[hub] 2025-07-17T19:54:40.522Z [INFO] All required environment variables are properly configured
[hub] 2025-07-17T19:54:40.530Z [INFO] 🚀 TravelViz Hub API server running on port 3001
[hub] 2025-07-17T19:54:40.530Z [INFO] 📍 Health check: http://localhost:3001/health
[hub] 2025-07-17T19:54:40.530Z [INFO] 🔧 Environment: development
[hub] 2025-07-17T19:54:40.530Z [INFO] 🌐 CORS enabled for: http://localhost:3000
⠼ Waiting for services to start...[web] 🔨 Compiling pages...
[web]  ○ Compiling / ...
⠴ Waiting for services to start...✅ [hub] Ready in 5.1s - http://localhost:3001
[hub] 2025-07-17T19:54:40.705Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/health",
[hub]   "statusCode": 200,
[hub]   "responseTime": "2.172ms",
[hub]   "ip": "::1"
[hub] }
[hub] GET /health 200 2.172ms 02215d76-8c33-4caf-a179-3983fc4f8c61
[web] 🔍 Middleware: No auth cookie found for /
⠙ Waiting for services to start...[web] 🔍 Middleware: No auth cookie found for /
⠴ Waiting for services to start...[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
⠧ Waiting for services to start...[web] ✨ Compilation complete
[web]  ✓ Compiled / in 3s (1452 modules)
⠹ Waiting for services to start...[web] Performance monitoring initialized
[web] Performance monitoring initialized
⠸ Waiting for services to start...[web] Performance monitoring initialized
[web] Performance monitoring initialized
⠼ Waiting for services to start...[web] Performance monitoring initialized
[web] Performance monitoring initialized
⠴ Waiting for services to start...[web] Performance monitoring initialized
[web]  GET / 200 in 1130ms
[web]  GET / 200 in 1132ms
⠏ Waiting for services to start...[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web]  ✓ Compiled in 1333ms (683 modules)
⠙ Waiting for services to start...[web] Performance monitoring initialized
[web] Performance monitoring initialized
✅ [web] Ready in 9.1s - http://localhost:3000

🚀 All services ready in 9.1s!
[hub] API: http://localhost:3001
[web] App: http://localhost:3000

Press Ctrl+C to stop all services

[web]  GET / 200 in 271ms
[web]  GET / 200 in 274ms
[web] 🔍 Middleware auth check: {
[web]   pathname: '/',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: false,
[web]   hasAccessToken: false,
[web]   cookieValue: '{"state":{"user":null,"accessToken":null,"refreshToken":null,"isAuthenticated":false},"version":0}...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: false,
[web]   authStateAccessToken: 'missing'
[web] }
[web] Performance monitoring initialized
[web]  GET / 200 in 96ms
[web] 🔍 Middleware auth check: {
[web]   pathname: '/logo-small.svg',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: false,
[web]   hasAccessToken: false,
[web]   cookieValue: '{"state":{"user":null,"accessToken":null,"refreshToken":null,"isAuthenticated":false},"version":0}...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: false,
[web]   authStateAccessToken: 'missing'
[web] }
[web] 🔍 Middleware: No auth cookie found for /site.webmanifest
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: false,
[web]   hasAccessToken: false,
[web]   cookieValue: '{"state":{"user":null,"accessToken":null,"refreshToken":null,"isAuthenticated":false},"version":0}...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: false,
[web]   authStateAccessToken: 'missing'
[web] }
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: false,
[web]   hasAccessToken: false,
[web]   cookieValue: '{"state":{"user":null,"accessToken":null,"refreshToken":null,"isAuthenticated":false},"version":0}...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: false,
[web]   authStateAccessToken: 'missing'
[web] }
[web] 🔍 Middleware auth check: {
[web]   pathname: '/login',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: false,
[web]   hasAccessToken: false,
[web]   cookieValue: '{"state":{"user":null,"accessToken":null,"refreshToken":null,"isAuthenticated":false},"version":0}...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: false,
[web]   authStateAccessToken: 'missing'
[web] }
[web] 🔨 Compiling pages...
[web]  ○ Compiling /login ...
[web] ✨ Compilation complete
[web]  ✓ Compiled /login in 1469ms (2632 modules)
[web]  GET /login 200 in 1675ms
[web] 🔍 Middleware: No auth cookie found for /site.webmanifest
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: false,
[web]   hasAccessToken: false,
[web]   cookieValue: '{"state":{"user":null,"accessToken":null,"refreshToken":null,"isAuthenticated":false},"version":0}...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: false,
[web]   authStateAccessToken: 'missing'
[web] }
[hub] 2025-07-17T19:55:23.515Z [INFO] Supabase connection pool initialized {
[hub]   "maxConnections": 50,
[hub]   "minConnections": 5
[hub] }
[hub] 2025-07-17T19:55:24.587Z [INFO] [AUDIT] {
[hub]   "eventType": "LOGIN_SUCCESS",
[hub]   "userId": "697b40b3-42d7-4b32-ad49-0220c2313643",
[hub]   "email": "<EMAIL>",
[hub]   "ipAddress": "::1",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "timestamp": "2025-07-17T19:55:24.587Z"
[hub] }
[hub] 2025-07-17T19:55:24.588Z [WARN] Slow API request {
[hub]   "method": "POST",
[hub]   "path": "/login",
[hub]   "duration": "1087.93ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T19:55:24.588Z [INFO] HTTP Request {
[hub]   "method": "POST",
[hub]   "url": "/login",
[hub]   "statusCode": 200,
[hub]   "responseTime": "1088.097ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] POST /api/v1/auth/login 200 1088.097ms 19ec92a4-070d-4cb7-bac7-e79e457e3be4
[web] 🔍 Middleware auth check: {
[web]   pathname: '/dashboard',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔨 Compiling pages...
[web]  ○ Compiling /dashboard ...
[web] ✨ Compilation complete
[web]  ✓ Compiled /dashboard in 1574ms (3774 modules)
[web]  GET /dashboard 200 in 1709ms
[web] 🔍 Middleware auth check: {
[web]   pathname: '/dashboard',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web]  GET /dashboard 200 in 16ms
[hub] 2025-07-17T19:55:26.549Z [WARN] Authentication failed - no token provided {
[hub]   "authHeader": "missing",
[hub]   "path": "/"
[hub] }
[hub] 2025-07-17T19:55:26.550Z [WARN] HTTP Request Error {
[hub]   "method": "GET",
[hub]   "url": "/?page=1&limit=20",
[hub]   "statusCode": 401,
[hub]   "responseTime": "1.286ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/trips?page=1&limit=20 401 1.286ms 5b5bef79-19a7-40c0-b24d-e287eacb4867
[web] 🔍 Middleware: No auth cookie found for /site.webmanifest
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔍 Middleware auth check: {
[web]   pathname: '/import',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔨 Compiling pages...
[web]  ○ Compiling /import ...
[web] ✨ Compilation complete
[web]  ✓ Compiled /import in 1972ms (5050 modules)
[web]  GET /import 200 in 2120ms
[web] 🔍 Middleware: No auth cookie found for /site.webmanifest
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[hub] 2025-07-17T19:55:36.450Z [INFO] Creating AIParserService {
[hub]   "hasGeminiKey": true,
[hub]   "hasOpenRouterKey": true,
[hub]   "nodeEnv": "development"
[hub] }
[hub] 2025-07-17T19:55:36.450Z [INFO] AIParserService initialized {
[hub]   "circuitBreakerState": "CLOSED",
[hub]   "enhancedAIRouterAvailable": true
[hub] }
[hub] Warning: Indexing all PDF objects
[hub] 2025-07-17T19:55:36.699Z [INFO] PDF text extracted successfully {
[hub]   "pages": 22,
[hub]   "textLength": 27230
[hub] }
[hub] 2025-07-17T19:55:37.014Z [INFO] Parse session created successfully {
[hub]   "sessionId": "3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "userId": "697b40b3-42d7-4b32-ad49-0220c2313643",
[hub]   "source": "gemini",
[hub]   "contentLength": 27230
[hub] }
[hub] 2025-07-17T19:55:37.076Z [INFO] [Cache] Redis connection pool cache initialized 
[hub] 2025-07-17T19:55:37.109Z [INFO] Redis connection pool initialized {
[hub]   "minConnections": 5,
[hub]   "totalConnections": 6
[hub] }
[hub] 2025-07-17T19:55:37.183Z [INFO] Starting background parse {
[hub]   "sessionId": "3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "source": "gemini"
[hub] }
[hub] 2025-07-17T19:55:37.184Z [INFO] parseAsync started {
[hub]   "sessionId": "3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "contentLength": 27230,
[hub]   "source": "gemini"
[hub] }
[hub] 2025-07-17T19:55:37.229Z [INFO] PDF import session created {
[hub]   "sessionId": "3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "userId": "697b40b3-42d7-4b32-ad49-0220c2313643",
[hub]   "source": "gemini",
[hub]   "pageCount": 22
[hub] }
[hub] 2025-07-17T19:55:37.229Z [WARN] Slow API request {
[hub]   "method": "POST",
[hub]   "path": "/pdf",
[hub]   "duration": "957.12ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T19:55:37.230Z [INFO] HTTP Request {
[hub]   "method": "POST",
[hub]   "url": "/pdf",
[hub]   "statusCode": 200,
[hub]   "responseTime": "957.353ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] POST /api/v1/import/pdf 200 957.353ms 53c91365-950c-44f2-98af-456f6860e1e8
[hub] 2025-07-17T19:55:37.259Z [INFO] Session status updated successfully {
[hub]   "sessionId": "3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "status": "processing",
[hub]   "progress": 10,
[hub]   "currentStep": "initializing"
[hub] }
[hub] 2025-07-17T19:55:37.295Z [INFO] Starting AI parse with Enhanced AI Router Service {
[hub]   "sessionId": "3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "contentLength": 27230,
[hub]   "source": "gemini",
[hub]   "hasGeminiKey": true,
[hub]   "hasOpenRouterKey": true
[hub] }
[hub] 2025-07-17T19:55:37.363Z [INFO] Session status updated successfully {
[hub]   "sessionId": "3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "status": "processing",
[hub]   "progress": 20,
[hub]   "currentStep": "extracting"
[hub] }
[hub] 2025-07-17T19:55:37.467Z [INFO] Session status updated successfully {
[hub]   "sessionId": "3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "status": "processing",
[hub]   "progress": 40,
[hub]   "currentStep": "parsing"
[hub] }
[hub] 2025-07-17T19:55:37.477Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "duration": "227.63ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T19:55:37.478Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 200,
[hub]   "responseTime": "227.933ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 200 227.933ms b2b5429e-ed0b-4488-9c79-c33c377f8865
[hub] 2025-07-17T19:55:37.505Z [INFO] Using Enhanced AI Router for intelligent model selection {
[hub]   "source": "gemini",
[hub]   "contentLength": 27230,
[hub]   "sessionId": "3a710928-3450-4e33-94d2-65a4005fa798"
[hub] }
[hub] 2025-07-17T19:55:37.506Z [INFO] Starting model selection {
[hub]   "contentLength": 27230,
[hub]   "complexity": "very_complex",
[hub]   "estimatedTokens": 14808
[hub] }
[hub] 2025-07-17T19:55:37.643Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "162.103ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 162.103ms 239ebd3f-9260-483d-8231-5d1d5e00bf6a
[hub] 2025-07-17T19:55:37.807Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "162.088ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 162.088ms 1b1c2d72-b19f-4e1a-8af6-588b6595f6c9
[hub] 2025-07-17T19:55:37.823Z [INFO] Model selected for parsing {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "provider": "moonshot",
[hub]   "reason": "Primary free tier model - under daily limit",
[hub]   "estimatedCost": 0,
[hub]   "userId": "3a710928-3450-4e33-94d2-65a4005fa798"
[hub] }
[hub] 2025-07-17T19:55:37.983Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "175.624ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 175.624ms de020049-8af6-4a0a-bb93-faf3c76208ad
[hub] 2025-07-17T19:55:38.139Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "155.273ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 155.273ms 9e2c7551-0add-4b1a-bab6-b7365d0ebd8d
[hub] 2025-07-17T19:55:38.300Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "159.677ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 159.677ms 9ecec264-5dbc-4999-8012-6abc25efda24
[hub] 2025-07-17T19:55:38.452Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "151.111ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 151.111ms 3265131e-22d7-4f88-97b8-5f376ee9b580
[hub] 2025-07-17T19:55:38.596Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "143.438ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 143.438ms 79874189-eaca-451a-a3d0-343227bf2b78
[hub] 2025-07-17T19:55:38.752Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "154.863ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 154.863ms f723b383-fdf1-4b1b-899f-bc36d962e1e9
[hub] 2025-07-17T19:55:38.893Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "140.796ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 140.796ms 477eccaf-a2e0-41f7-a162-5cface39bc44
[hub] 2025-07-17T19:55:39.043Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "149.448ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 149.448ms 615097b3-4d4e-41b5-b941-05f8cffc425b
[hub] 2025-07-17T19:55:39.202Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "157.329ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 157.329ms f16cfa9b-4b27-4f3a-80ff-2952e3cad0c5
[hub] 2025-07-17T19:55:39.373Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "170.298ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 170.298ms da088812-bac8-4b12-8bec-a314b9bb5962
[hub] 2025-07-17T19:55:39.533Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "159.611ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 159.611ms 6715b04c-f68b-48c6-bcb5-e3d9042a9d87
[hub] 2025-07-17T19:55:39.688Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "153.799ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 153.799ms 05451198-7d8e-4d58-aeda-faf700fe03a2
[hub] 2025-07-17T19:55:39.837Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "148.591ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 148.591ms 680bb0af-da59-4c0b-b0cd-7738287bd614
[hub] 2025-07-17T19:55:39.976Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "137.676ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 137.676ms 921e1122-a0cd-4c15-9ad7-0ca9437ad06c
[hub] 2025-07-17T19:55:40.123Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "146.642ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 146.642ms c5ca1996-a78d-40ac-9771-5cdb55010d1a
[hub] 2025-07-17T19:55:40.264Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "140.085ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 140.085ms fdaa2a84-05ac-45f3-a5b7-2a5cedbd762a
[hub] 2025-07-17T19:55:40.409Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "144.516ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 144.516ms e6942021-91be-4b64-82ea-73287791f197
[hub] 2025-07-17T19:55:40.571Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "160.805ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 160.805ms 38d1940a-c281-4b6b-a8c5-0a62f60153bf
[hub] 2025-07-17T19:55:40.747Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "175.283ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 175.283ms 2f9169f7-7e2c-4392-b07a-75960378a4f9
[hub] 2025-07-17T19:55:40.902Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "153.77ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 153.770ms 494a7644-e1a8-4536-972d-b456077044fa
[hub] 2025-07-17T19:55:41.051Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "148.492ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 148.492ms 2b245081-715e-496f-9c50-792115da8399
[hub] 2025-07-17T19:55:41.203Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "150.967ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 150.967ms 387db3f7-8c41-40fb-b6ea-8fc94bfe4789
[hub] 2025-07-17T19:55:41.361Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "156.805ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 156.805ms 559bbe8f-d52f-4b49-a093-f9a0f8d90f38
[hub] 2025-07-17T19:55:41.496Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "134.028ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 134.028ms f8afde5e-e791-4e4c-a9cd-ae7553387e68
[hub] 2025-07-17T19:55:41.672Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "174.928ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 174.928ms 3861ea19-cd75-4b50-8024-baab958c3db7
[hub] 2025-07-17T19:55:41.827Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "154.827ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 154.827ms c43488b4-553b-45fa-9934-37c3e7f5e942
[hub] 2025-07-17T19:55:41.982Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "153.742ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 153.742ms 554d6976-8d8b-478c-93c8-115105c2a30f
[hub] 2025-07-17T19:55:42.145Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "161.74ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 161.740ms 628624b5-aa5e-4567-ba02-38ebb2b497fc
[hub] 2025-07-17T19:55:42.298Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "151.933ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 151.933ms 66c04f84-d764-4c83-a6ad-f609301ff129
[hub] 2025-07-17T19:55:42.439Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "140.155ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 140.155ms 2cbde932-2453-4465-884c-330e2e2ab982
[hub] 2025-07-17T19:55:42.622Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "182.082ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 182.082ms 42b07297-9887-45f8-bbb0-72c839f6330f
[hub] 2025-07-17T19:55:42.825Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "duration": "202.76ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T19:55:42.826Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "203.024ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 203.024ms b621e270-6915-41b9-9af6-ef91c5cdef71
[hub] 2025-07-17T19:55:43.015Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "188.584ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 188.584ms 6b2b61b9-2661-432e-ac29-11c88a1a1641
[hub] 2025-07-17T19:55:43.161Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "145.184ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 145.184ms 178e7269-5d02-4a23-b9f0-f55912fb340c
[hub] 2025-07-17T19:55:43.306Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "143.722ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 143.722ms 90a1420b-7162-436f-8ec4-c749cac3940f
[hub] 2025-07-17T19:55:43.479Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "170.326ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 170.326ms 4a04d018-3a99-4141-bdf5-42001e35cd2e
[hub] 2025-07-17T19:55:43.717Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "duration": "237.30ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T19:55:43.717Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "237.528ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 237.528ms 42a66e9b-8642-4f55-b442-4c39270dbb48
[hub] 2025-07-17T19:55:43.880Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "162.081ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 162.081ms cdc8f038-ffe2-4453-8803-910e9d7b67a4
[hub] 2025-07-17T19:55:44.059Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "177.885ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 177.885ms 72fdbdfe-b29f-439e-955c-c4cca0485625
[hub] 2025-07-17T19:55:44.210Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "150.418ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 150.418ms b4409277-032c-43f1-aa26-b6e0c41cbdcf
[hub] 2025-07-17T19:55:44.394Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "181.92ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 181.920ms be68c9d9-d85a-48f0-9d34-eaa187068e8e
[hub] 2025-07-17T19:55:44.574Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "179.143ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 179.143ms 56f60397-4f1d-4c64-9608-51d8082b9061
[hub] 2025-07-17T19:55:44.722Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "147.526ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 147.526ms 886b140f-904b-4856-a216-8359381e7fcf
[hub] 2025-07-17T19:55:44.882Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "159.577ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 159.577ms a3737315-e54b-44ef-8e9c-518dd8ce174c
[hub] 2025-07-17T19:55:45.026Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "142.91ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 142.910ms 84ee36b1-d002-463b-a6b9-e8d269ad58a1
[hub] 2025-07-17T19:55:45.206Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "178.512ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 178.512ms eb8d1e35-89c1-4b24-8b3c-a5f82f5cf66c
[hub] 2025-07-17T19:55:45.382Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "175.268ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 175.268ms 263ae1f3-9731-496b-94d5-4a2534bcd7fe
[hub] 2025-07-17T19:55:45.545Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "162.314ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 162.314ms 18ec6341-76de-415b-af56-919f6a237d0c
[hub] 2025-07-17T19:55:45.697Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/3a710928-3450-4e33-94d2-65a4005fa798",
[hub]   "statusCode": 304,
[hub]   "responseTime": "151.202ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/3a710928-3450-4e33-94d2-65a4005fa798 304 151.202ms fa60a6fa-1799-40f7-8a1a-635aa59981d3


Shutting down services...
[hub] Process exited with code null
Terminate batch job (Y/N)?
PS C:\Users\<USER>\Travelviz\Travelviz> 